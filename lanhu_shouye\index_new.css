/* 页面基础样式 */
.page {
	width: 100%;
	min-height: 100vh;
	background-color: #ffffff;
}

.main-content {
	max-width: 1920px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
}

/* 顶部导航栏 */
.header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 23px 0;
	height: 92px;
	background-color: #ffffff;
	width: 1025px;
	margin: 0 auto;
}

.logo-section {
	display: flex;
	align-items: center;
	gap: 12px;
}

.logo-icon {
	width: 42px;
	height: 42px;
	border-radius: 50%;
	background-image: url(./img/79b144a48e324afe8d417c938894a984_mergeImage.png);
	background-size: cover;
}

.company-name {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	color: #000000;
	margin: 0;
	line-height: 42px;
}

.main-nav {
	display: flex;
	align-items: center;
	gap: 40px;
}

.nav-link {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	text-decoration: none;
	line-height: 22px;
	transition: color 0.3s ease;
}

.nav-link:hover {
	color: #0055c3;
}

.nav-icon {
	width: 14px;
	height: 14px;
	margin-left: 40px;
}

/* 二级导航栏 */
.secondary-nav {
	display: flex;
	align-items: center;
	width: 100%;
	height: 92px;
}

.secondary-nav-left {
	flex: 1;
	background-color: transparent;
	height: 100%;
}

.secondary-nav-right {
	flex: 1;
	background-color: #090909;
	height: 100%;
}

.category-highlight {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 118px;
	height: 92px;
	background-color: #0055c3;
	box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
}

.category-name {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ffffff;
	line-height: 25px;
}

.product-nav {
	display: flex;
	align-items: center;
	background-color: #090909;
	height: 92px;
	width: 1025px;
	position: relative;
	padding: 0 32px;
	gap: 50px;
}

.product-link {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ffffff;
	text-decoration: none;
	line-height: 25px;
	transition: color 0.3s ease;
}

.product-link:hover {
	color: #0055c3;
}

.contact-sales-btn {
	background-color: #ffffff;
	color: #000000;
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 30px;
	border: none;
	padding: 31px 26px;
	margin-left: auto;
	margin-right: 32px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.contact-sales-btn:hover {
	background-color: #f0f0f0;
}

.nav-divider {
	position: absolute;
	left: -84px;
	top: 45px;
	width: 94px;
	height: 1px;
}

/* 英雄区域 */
.hero-section {
	background-image: url(./img/aedda77a45764f4a9a84182ecfa9e1d7_mergeImage.png);
	background-size: cover;
	background-position: center;
	width: 1440px;
	height: 382px;
	margin: 0 auto;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 55px;
	text-align: center;
}

.hero-title {
	font-size: 48px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	color: #ffffff;
	line-height: 65px;
	margin: 0;
	max-width: 900px;
}

.cta-button {
	background-color: #ec2914;
	color: #ffffff;
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 22px;
	border: none;
	padding: 12px 19px;
	height: 46px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.cta-button:hover {
	background-color: #d12410;
}

.hero-decoration {
	width: 128px;
	height: 14px;
}

/* 产品预览区域 */
.product-preview {
	background: linear-gradient(271deg, #e6b2b2 0%, #ffe6e6 100%);
	background-size: cover;
	background-position: center;
	height: 680px;
	margin-top: 60px;
	padding: 57px 0;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
}

.product-preview-container {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	max-width: 1250px;
	position: relative;
}

.product-preview-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 32px;
	flex: 1;
	margin: 0 156px;
}

.nav-arrow {
	background: none;
	border: none;
	cursor: pointer;
	padding: 10px;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: opacity 0.3s ease;
}

.nav-arrow:hover {
	opacity: 0.7;
}

.nav-arrow img {
	width: 40px;
	height: 40px;
	object-fit: contain;
}

.nav-arrow-left {
	margin-right: 20px;
}

.nav-arrow-right {
	margin-left: 20px;
}

.section-header {
	display: flex;
	justify-content: center;
}

.section-title {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 42px;
	margin: 0;
}

.product-categories {
	display: flex;
	gap: 80px;
	justify-content: center;
}

.category-link {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	text-decoration: none;
	line-height: 20px;
	transition: color 0.3s ease;
}

.category-link:hover {
	color: #0055c3;
}

.product-gallery {
	width: 100%;
	display: flex;
	align-items: center;
	gap: 38px;
	justify-content: flex-end;
}

.main-product-image {
	width: 600px;
	height: 300px;
	border-radius: 8px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.main-product-image img {
	max-width: 100%;
	max-height: 100%;
	object-fit: contain;
}

.product-thumbnails {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.product-image-item {
	width: 105px;
	height: 102px;
}

.product-thumbnail {
	width: 100%;
	height: 100%;
	border-radius: 8px;
	box-shadow: 0px 0px 6px 0px rgba(113, 113, 113, 0.3);
	object-fit: cover;
	cursor: pointer;
	transition: transform 0.3s ease;
}

.product-thumbnail:hover {
	transform: scale(1.05);
}

.product-hover-demo {
	display: flex;
	align-items: flex-end;
	gap: 887px;
	width: 100%;
	max-width: 1061px;
}

.hover-label {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ff1d1d;
	line-height: 20px;
}

.product-config {
	width: 100%;
	display: flex;
	align-items: center;
	gap: 16px;
	margin-top: 60px;
	padding: 0 20px;
	justify-content: space-between;
}

.config-tabs {
	display: flex;
	/* flex-direction: column; */
	gap: 40px;
}

.config-tab {
	background-color: #000000;
	color: #ffffff;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 20px;
	border: none;
	padding: 7px 18px;
	height: 34px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.config-tab:not(.active) {
	background-color: transparent;
	color: #000000;
	border: 1px solid #000000;
}

.config-tab:hover {
	background-color: #333333;
	color: #fff;
}

.product-detail-image {
	width: 71px;
	height: 53px;
	margin-top: 25px;
}

.product-specs {
	display: flex;
	align-items: center;
	gap: 30px;
}

.spec-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 9px;
}

.spec-value {
	font-size: 18px;
	font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
	color: #000000;
	line-height: 21px;
}

.spec-label {
	font-size: 12px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	color: #000000;
	line-height: 17px;
}

.spec-divider {
	width: 1px;
	height: 47px;
	border-left: 1px solid #000000;
}

/* 公司介绍区域 */
.company-intro {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 15px;
	padding-top: 57px;
	text-align: center;
}

.company-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 50px;
	font-weight: 400;
	margin: 0;
}

.company-description {
	font-size: 20px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 27px;
	margin: 0;
	text-align: right;
	max-width: 910px;
}

/* 解决方案区域 */
.solutions-section {
	background-color: #000000;
	height: 120px;
	margin: 56px auto 0;
	display: flex;
	align-items: center;
	justify-content: center;
}

.solutions-content {
	display: flex;
	width: 1030px;
	/* align-items: center; */
}

.solutions-title {
	text-align: center;
	flex: 1;
	font-size: 24px;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	color: #ffffff;
	line-height: 120px;
	margin: 0;
}

.solutions-btn {
	text-align: center;
	flex: 1;
	background-image: url(./img/11c09b6d4d974cfdb0174ba7f2c9e083_mergeImage.png);
	background-size: cover;
	color: #ffffff;
	font-size: 24px;
	font-family: PingFangSC-Medium;
	font-weight: 500;
	border: none;
	line-height: 120px;
	cursor: pointer;
	transition: opacity 0.3s ease;
}

.solutions-btn:hover {
	opacity: 0.8;
}

/* 新闻区域 */
.news-section {
	padding: 60px 0;
	max-width: 1030px;
	margin: 0 auto;
}

.news-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 40px;
	padding-left: 77px;
}

.news-title {
	font-size: 36px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 50px;
	margin: 0;
	font-weight: 500;
}

.news-nav {
	display: flex;
	align-items: center;
	gap: 10px;
}

.browse-all-link {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #767676;
	text-decoration: none;
	line-height: 20px;
	transition: color 0.3s ease;
}

.browse-all-link.active {
	color: #003a85;
}

.browse-all-link:hover {
	color: #0055c3;
}

.nav-arrow {
	width: 14px;
	height: 14px;
}

.news-divider {
	width: 77px;
	height: 1px;
	margin: 0 20px;
}

.news-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 20px;
	max-width: 1160px;
	margin: 0 auto;
}

.news-card {
	background-color: #ffffff;
	overflow: hidden;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease, box-shadow 0.3s ease;
	display: flex;
	flex-direction: column;
	height: 338px;
}

.news-card:hover {
	transform: translateY(-4px);
	box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.news-image {
	width: 100%;
	height: 128px;
	background-image: url(./img/bd42d2e45a7b44e6a106f1b6668ebc72_mergeImage.png);
	background-size: cover;
	background-position: center;
	flex-shrink: 0;
}

.news-image.secondary {
	background-image: url(./img/6e327735808b4c35be47442543b0094a_mergeImage.png);
}

.news-image.tertiary {
	background-image: url(./img/050735b55ad74444aa753f87269e74f6_mergeImage.png);
}

.news-image.quaternary {
	background-image: url(./img/a40c4aded9fe41128fc2d24e4588ad00_mergeImage.png);
}

.news-content {
	padding: 20px;
	display: flex;
	flex-direction: column;
	gap: 10px;
	flex: 1;
}

.news-card-title {
	font-size: 18px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 24px;
	margin: 0;
	font-weight: 500;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

.news-card-summary {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Light;
	font-weight: 300;
	color: #666666;
	line-height: 20px;
	margin: 0;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 1200px) {
	.news-grid {
		grid-template-columns: repeat(3, 1fr);
		max-width: 900px;
	}
}

@media (max-width: 900px) {
	.news-grid {
		grid-template-columns: repeat(2, 1fr);
		max-width: 600px;
	}

	.news-header {
		flex-direction: column;
		gap: 20px;
		text-align: center;
	}
}

@media (max-width: 600px) {
	.news-grid {
		grid-template-columns: 1fr;
		max-width: 400px;
	}

	.news-section {
		padding: 40px 0;
	}

	.news-title {
		font-size: 28px;
	}
}

/* 联系我们区域 */
.contact-section {
	background: url(./img/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158.png)
		100% no-repeat;
	background-size: 100% 100%;
	height: 170px;
	margin: 60px 433px 0;
	padding: 26px 50px 60px;
	display: flex;
	flex-direction: column;
	gap: 3px;
}

.contact-header {
	margin-bottom: 3px;
}

.contact-title {
	font-size: 24px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 33px;
	margin: 0;
}

.contact-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 120px;
}

.contact-description {
	font-size: 14px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #000000;
	line-height: 20px;
	margin: 0;
	max-width: 639px;
}

.contact-btn {
	background-color: #ec2914;
	color: #ffffff;
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	line-height: 22px;
	border: none;
	padding: 12px 19px;
	width: 100px;
	height: 46px;
	cursor: pointer;
	transition: background-color 0.3s ease;
}

.contact-btn:hover {
	background-color: #d12410;
}

/* 页脚 */
.footer {
	background: url(./img/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054.png)
		100% no-repeat;
	background-size: 100% 100%;
	height: 230px;
	margin: 86px 433px 0;
	padding: 18px 60px 3px;
	position: relative;
	display: flex;
	flex-direction: column;
	gap: 30px;
}

.footer-links {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 110px;
	margin-bottom: 12px;
}

.footer-column {
	display: flex;
	flex-direction: column;
	gap: 12px;
}

.footer-column-title {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Bold;
	font-weight: 700;
	color: #ffffff;
	line-height: 22px;
	margin: 0;
}

.footer-link {
	font-size: 16px;
	font-family: AlibabaPuHuiTi-Regular;
	color: #ffffff;
	text-decoration: none;
	line-height: 22px;
	transition: color 0.3s ease;
}

.footer-link:hover {
	color: #0055c3;
}

.footer-brand {
	margin: 30px 0 3px 20px;
}

.footer-logo {
	display: flex;
	align-items: center;
	gap: 4px;
}

.footer-logo-image {
	width: 56px;
	height: 55px;
}

.footer-company-name {
	font-size: 30px;
	font-family: AlibabaPuHuiTi-Medium;
	font-weight: 500;
	color: #000000;
	line-height: 42px;
}

.footer-bottom {
	background-color: #000000;
	position: absolute;
	left: 246px;
	top: 170px;
	width: 1241px;
	height: 60px;
}
